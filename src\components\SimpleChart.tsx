import React, { useEffect, useRef } from 'react'
import { createChart, ColorType } from 'lightweight-charts'

interface SimpleChartProps {
  symbol: string
}

const SimpleChart: React.FC<SimpleChartProps> = ({ symbol }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartContainerRef.current) {
      console.log('Chart container not available')
      return
    }

    try {
      console.log('Creating chart...')
      const chart = createChart(chartContainerRef.current, {
        width: 600,
        height: 400,
        layout: {
          background: { type: ColorType.Solid, color: '#1e1e1e' },
          textColor: '#d1d4dc',
        },
      })

      // Add simple line series
      const lineSeries = chart.addLineSeries({
        color: '#2962ff',
      })

      // Add some sample data
      const data = [
        { time: '2023-01-01', value: 100 },
        { time: '2023-01-02', value: 102 },
        { time: '2023-01-03', value: 98 },
        { time: '2023-01-04', value: 105 },
        { time: '2023-01-05', value: 103 },
      ]

      lineSeries.setData(data)
      console.log('Chart created successfully')

      return () => {
        chart.remove()
      }
    } catch (error) {
      console.error('Error creating chart:', error)
    }
  }, [])



  return (
    <div style={{ padding: '20px' }}>
      <h3>Simple Chart Test for {symbol}</h3>
      <div 
        ref={chartContainerRef} 
        style={{ 
          width: '600px', 
          height: '400px', 
          border: '1px solid #404040',
          margin: '0 auto'
        }}
      />
    </div>
  )
}

export default SimpleChart
