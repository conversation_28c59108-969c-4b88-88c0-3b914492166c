import React, { useEffect, useRef, useState } from 'react'
import {
  create<PERSON>hart,
  ColorType,
  CrosshairMode,
  CandlestickSeries,
  HistogramSeries
} from 'lightweight-charts'
import type {
  IChartApi,
  ISeriesApi,
  CandlestickData
} from 'lightweight-charts'
import type { ChartData } from '../types/tradingview'
import './TradingViewChart.css'

interface TradingViewChartProps {
  data: ChartData[]
  symbol: string
  width?: number
  height?: number
}

const TradingViewChart: React.FC<TradingViewChartProps> = ({ 
  data, 
  symbol, 
  width = 800, 
  height = 500 
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)
  const [chartType, setChartType] = useState<'candlestick' | 'line'>('candlestick')
  const [isFullscreen, setIsFullscreen] = useState(false)

  useEffect(() => {
    if (!chartContainerRef.current) return

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: isFullscreen ? window.innerWidth : width,
      height: isFullscreen ? window.innerHeight : height,
      layout: {
        background: { type: ColorType.Solid, color: '#1e1e1e' },
        textColor: '#d1d4dc',
      },
      grid: {
        vertLines: { color: '#2a2a2a' },
        horzLines: { color: '#2a2a2a' },
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
      rightPriceScale: {
        borderColor: '#485c7b',
      },
      timeScale: {
        borderColor: '#485c7b',
        timeVisible: true,
        secondsVisible: false,
      },
    })

    chartRef.current = chart

    // Add candlestick series
    const candlestickSeries = chart.addSeries(CandlestickSeries, {
      upColor: '#4caf50',
      downColor: '#f44336',
      borderDownColor: '#f44336',
      borderUpColor: '#4caf50',
      wickDownColor: '#f44336',
      wickUpColor: '#4caf50',
    })

    candlestickSeriesRef.current = candlestickSeries

    // Add volume series
    const volumeSeries = chart.addSeries(HistogramSeries, {
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
    })

    volumeSeriesRef.current = volumeSeries

    // Position volume series at the bottom
    chart.priceScale('').applyOptions({
      scaleMargins: {
        top: 0.7,
        bottom: 0,
      },
    })

    // Handle resize
    const handleResize = () => {
      if (chart && chartContainerRef.current) {
        chart.applyOptions({
          width: isFullscreen ? window.innerWidth : chartContainerRef.current.clientWidth,
          height: isFullscreen ? window.innerHeight : height,
        })
      }
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chart) {
        chart.remove()
      }
    }
  }, [width, height, isFullscreen])

  useEffect(() => {
    if (!candlestickSeriesRef.current || !volumeSeriesRef.current) return

    // Convert data for candlestick chart
    const candlestickData: CandlestickData[] = data.map(item => ({
      time: item.time,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
    }))

    // Convert data for volume
    const volumeData = data.map(item => ({
      time: item.time,
      value: item.volume,
      color: item.close >= item.open ? '#4caf5080' : '#f4433680',
    }))

    candlestickSeriesRef.current.setData(candlestickData)
    volumeSeriesRef.current.setData(volumeData)

    // Fit content
    if (chartRef.current) {
      chartRef.current.timeScale().fitContent()
    }
  }, [data])

  const toggleChartType = () => {
    setChartType(prev => prev === 'candlestick' ? 'line' : 'candlestick')
    // Note: For simplicity, we're keeping candlestick. 
    // In a full implementation, you'd switch between series types
  }

  const toggleFullscreen = () => {
    setIsFullscreen(prev => !prev)
  }

  return (
    <div className={`chart-wrapper ${isFullscreen ? 'fullscreen' : ''}`}>
      <div className="chart-controls">
        <div className="chart-info">
          <h3>{symbol}</h3>
          <span className="chart-type">{chartType}</span>
        </div>
        <div className="chart-buttons">
          <button onClick={toggleChartType} className="control-btn">
            {chartType === 'candlestick' ? 'Line' : 'Candles'}
          </button>
          <button onClick={toggleFullscreen} className="control-btn">
            {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
          </button>
        </div>
      </div>
      <div 
        ref={chartContainerRef} 
        className="chart-container"
        style={{ 
          width: isFullscreen ? '100vw' : width,
          height: isFullscreen ? '100vh' : height 
        }}
      />
    </div>
  )
}

export default TradingViewChart
