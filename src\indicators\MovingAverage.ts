import type { ChartData } from '../types/tradingview'

export interface MovingAverageData {
  time: string
  value: number
}

export interface MovingAverageOptions {
  period: number
  type: 'SMA' | 'EMA' | 'WMA'
  color: string
  lineWidth: number
}

/**
 * Simple Moving Average (SMA) calculation
 */
export function calculateSMA(data: ChartData[], period: number): MovingAverageData[] {
  const result: MovingAverageData[] = []
  
  for (let i = period - 1; i < data.length; i++) {
    let sum = 0
    for (let j = 0; j < period; j++) {
      sum += data[i - j].close
    }
    
    result.push({
      time: data[i].time,
      value: sum / period
    })
  }
  
  return result
}

/**
 * Exponential Moving Average (EMA) calculation
 */
export function calculateEMA(data: ChartData[], period: number): MovingAverageData[] {
  const result: MovingAverageData[] = []
  const multiplier = 2 / (period + 1)
  
  if (data.length === 0) return result
  
  // First EMA value is the SMA
  let ema = data[0].close
  for (let i = 1; i < Math.min(period, data.length); i++) {
    ema = (ema * (period - 1) + data[i].close) / period
  }
  
  result.push({
    time: data[Math.min(period - 1, data.length - 1)].time,
    value: ema
  })
  
  // Calculate EMA for remaining data points
  for (let i = period; i < data.length; i++) {
    ema = (data[i].close - ema) * multiplier + ema
    result.push({
      time: data[i].time,
      value: ema
    })
  }
  
  return result
}

/**
 * Weighted Moving Average (WMA) calculation
 */
export function calculateWMA(data: ChartData[], period: number): MovingAverageData[] {
  const result: MovingAverageData[] = []
  const weightSum = (period * (period + 1)) / 2
  
  for (let i = period - 1; i < data.length; i++) {
    let weightedSum = 0
    for (let j = 0; j < period; j++) {
      weightedSum += data[i - j].close * (period - j)
    }
    
    result.push({
      time: data[i].time,
      value: weightedSum / weightSum
    })
  }
  
  return result
}

/**
 * Calculate moving average based on type
 */
export function calculateMovingAverage(
  data: ChartData[], 
  options: MovingAverageOptions
): MovingAverageData[] {
  switch (options.type) {
    case 'SMA':
      return calculateSMA(data, options.period)
    case 'EMA':
      return calculateEMA(data, options.period)
    case 'WMA':
      return calculateWMA(data, options.period)
    default:
      return calculateSMA(data, options.period)
  }
}
