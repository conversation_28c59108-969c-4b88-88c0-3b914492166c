# TradingView Lightweight Charts - React App

A modern web application demonstrating the integration of TradingView's Lightweight Charts library with React and TypeScript. This project showcases professional financial charting capabilities with a clean, responsive interface.

## 🚀 Features

- **Professional Financial Charts**: Built with TradingView's Lightweight Charts library
- **React + TypeScript**: Modern development stack with full type safety
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Real-time Simulation**: Simulated real-time data updates and loading states
- **Multiple Chart Types**: Candlestick charts with volume indicators
- **Interactive Controls**: Symbol selection, data refresh, and fullscreen mode
- **Sample Data**: Realistic financial data generation for demonstration

## 📦 Tech Stack

- **Frontend Framework**: React 19
- **Language**: TypeScript
- **Build Tool**: Vite
- **Charts Library**: TradingView Lightweight Charts 5.0
- **Styling**: CSS3 with CSS Variables
- **Package Manager**: npm

## 🛠️ Installation & Setup

1. **Clone or navigate to the project directory**:
   ```bash
   cd c:\scripts\tradingview
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser** and navigate to `http://localhost:3000`

## 📁 Project Structure

```
src/
├── components/
│   ├── TradingViewChart.tsx    # Main chart component
│   ├── TradingViewChart.css    # Chart styling
│   ├── LoadingSpinner.tsx      # Loading component
│   └── LoadingSpinner.css      # Loading spinner styles
├── data/
│   └── sampleData.ts          # Sample data generation
├── types/
│   └── tradingview.ts         # TypeScript type definitions
├── App.tsx                    # Main application component
├── App.css                    # Application styles
├── main.tsx                   # React entry point
└── index.css                  # Global styles
```

## 🎯 Key Components

### TradingViewChart Component
- Integrates TradingView Lightweight Charts
- Supports candlestick charts with volume
- Responsive design with fullscreen capability
- Real-time data updates

### Sample Data Generator
- Generates realistic OHLC (Open, High, Low, Close) data
- Includes volume data
- Simulates market volatility and trends
- Supports multiple time intervals

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## 📊 Chart Features

- **Candlestick Charts**: Professional OHLC visualization
- **Volume Indicators**: Trading volume histogram
- **Interactive Crosshair**: Precise data point inspection
- **Zoom & Pan**: Navigate through historical data
- **Responsive Layout**: Adapts to different screen sizes
- **Dark Theme**: Professional trading interface

## 🎨 Customization

### Styling
The application uses CSS variables for easy theming:
```css
:root {
  --primary-color: #2962ff;
  --background-dark: #1e1e1e;
  --background-light: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
}
```

### Chart Configuration
Modify chart settings in `TradingViewChart.tsx`:
```typescript
const chart = createChart(container, {
  layout: {
    background: { type: ColorType.Solid, color: '#1e1e1e' },
    textColor: '#d1d4dc',
  },
  // ... other options
})
```

## 🔄 Upgrading to TradingView Advanced Charts

This project is designed to be easily upgradeable to TradingView's Advanced Charts library when you obtain access:

1. **Request Access**: Visit [TradingView's website](https://www.tradingview.com/HTML5-stock-forex-bitcoin-charting-library/) to request access to Advanced Charts
2. **Install Advanced Charts**: Replace Lightweight Charts with the Advanced Charts library
3. **Update Components**: Modify the chart component to use Advanced Charts API
4. **Add Features**: Implement additional features like indicators, drawing tools, etc.

## 📚 Learning Resources

- [TradingView Lightweight Charts Documentation](https://tradingview.github.io/lightweight-charts/)
- [TradingView Advanced Charts Documentation](https://www.tradingview.com/charting-library-docs/)
- [React Documentation](https://react.dev/)
- [TypeScript Documentation](https://www.typescriptlang.org/)

## 🤝 Contributing

Feel free to contribute to this project by:
- Adding new chart types
- Implementing real data feeds
- Improving the UI/UX
- Adding more technical indicators
- Enhancing mobile responsiveness

## 📄 License

This project is licensed under the MIT License. Note that TradingView Lightweight Charts is licensed under Apache 2.0.

## 🙏 Acknowledgments

- [TradingView](https://www.tradingview.com/) for the excellent charting libraries
- [React Team](https://react.dev/) for the amazing framework
- [Vite Team](https://vitejs.dev/) for the fast build tool
