{"name": "tradingview-charts-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"lightweight-charts": "^5.0.7", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1", "typescript": "~5.8.3", "vite": "^6.3.5"}, "description": "TradingView Lightweight Charts integration with React and TypeScript", "keywords": ["tradingview", "charts", "react", "typescript", "financial"], "author": "", "license": "MIT"}