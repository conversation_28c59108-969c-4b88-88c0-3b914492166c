import { ChartData, DatafeedConfiguration, SymbolInfo } from '../types/tradingview'
import { generateSampleData } from '../data/sampleData'

/**
 * Simple datafeed implementation for TradingView Lightweight Charts
 * This is a basic structure that can be extended for real data sources
 */
export class SimpleDatafeed {
  private configuration: DatafeedConfiguration = {
    supported_resolutions: ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'],
    supports_group_request: false,
    supports_marks: false,
    supports_search: true,
    supports_timescale_marks: false,
  }

  /**
   * Get datafeed configuration
   */
  getConfiguration(): DatafeedConfiguration {
    return this.configuration
  }

  /**
   * Search for symbols (mock implementation)
   */
  async searchSymbols(userInput: string): Promise<SymbolInfo[]> {
    const symbols = [
      'AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN', 
      'NVDA', 'META', 'NFLX', 'AMD', 'INTC'
    ]
    
    const filtered = symbols.filter(symbol => 
      symbol.toLowerCase().includes(userInput.toLowerCase())
    )
    
    return filtered.map(symbol => this.createSymbolInfo(symbol))
  }

  /**
   * Get symbol information
   */
  getSymbolInfo(symbol: string): SymbolInfo {
    return this.createSymbolInfo(symbol)
  }

  /**
   * Get historical data for a symbol
   */
  async getHistoricalData(
    symbol: string,
    resolution: string,
    from: number,
    to: number
  ): Promise<ChartData[]> {
    // In a real implementation, this would fetch data from an API
    // For now, we return sample data
    console.log(`Fetching data for ${symbol} from ${new Date(from * 1000)} to ${new Date(to * 1000)}`)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    return generateSampleData(100)
  }

  /**
   * Subscribe to real-time data updates
   */
  subscribeBars(
    symbol: string,
    resolution: string,
    onRealtimeCallback: (bar: ChartData) => void,
    subscriberUID: string
  ): void {
    console.log(`Subscribing to real-time data for ${symbol}`)
    
    // In a real implementation, this would establish a WebSocket connection
    // For demo purposes, we'll simulate updates every 5 seconds
    const interval = setInterval(() => {
      const lastBar = generateSampleData(1)[0]
      onRealtimeCallback(lastBar)
    }, 5000)

    // Store interval for cleanup (in a real app, you'd manage subscriptions properly)
    ;(window as any)[`interval_${subscriberUID}`] = interval
  }

  /**
   * Unsubscribe from real-time data updates
   */
  unsubscribeBars(subscriberUID: string): void {
    console.log(`Unsubscribing from real-time data: ${subscriberUID}`)
    
    const interval = (window as any)[`interval_${subscriberUID}`]
    if (interval) {
      clearInterval(interval)
      delete (window as any)[`interval_${subscriberUID}`]
    }
  }

  /**
   * Create symbol info object
   */
  private createSymbolInfo(symbol: string): SymbolInfo {
    return {
      name: symbol,
      full_name: `${symbol} Stock`,
      description: `${symbol} Corporation`,
      type: 'stock',
      session: '0930-1600',
      exchange: 'NASDAQ',
      listed_exchange: 'NASDAQ',
      timezone: 'America/New_York',
      format: 'price',
      pricescale: 100,
      minmov: 1,
      fractional: false,
      minmove2: 0,
      currency_code: 'USD',
      original_currency_code: 'USD',
      unit_id: '',
      original_unit_id: '',
      unit_conversion_types: [],
      original_type: 'stock',
      original_session: '0930-1600',
      original_exchange: 'NASDAQ',
      original_listed_exchange: 'NASDAQ',
      original_format: 'price',
      original_pricescale: 100,
      original_minmov: 1,
      original_fractional: false,
      original_minmove2: 0,
      has_intraday: true,
      has_no_volume: false,
      has_weekly_and_monthly: true,
      has_empty_bars: false,
      force_session_rebuild: false,
      has_seconds: false,
      seconds_multipliers: [],
      has_daily: true,
      intraday_multipliers: ['1', '5', '15', '30', '60'],
      weekly_multipliers: ['1'],
      monthly_multipliers: ['1'],
      supported_resolutions: ['1', '5', '15', '30', '60', '240', '1D', '1W', '1M'],
      volume_precision: 0,
      data_status: 'streaming',
      expired: false,
      expiration_date: 0,
      sector: 'Technology',
      industry: 'Software',
      currency_id: 'USD',
      original_currency_id: 'USD'
    }
  }
}

// Export a singleton instance
export const datafeed = new SimpleDatafeed()
