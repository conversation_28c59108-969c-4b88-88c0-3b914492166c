import React, { useEffect, useRef, useState } from 'react'
import {
  create<PERSON><PERSON>,
  ColorType,
  CrosshairMode
} from 'lightweight-charts'
import type {
  IChartApi,
  ISeriesApi,
  CandlestickData,
  LineData,
  HistogramData
} from 'lightweight-charts'
import type { ChartData } from '../types/tradingview'
import { calculateMovingAverage, type MovingAverageOptions } from '../indicators/MovingAverage'
import { calculateRSI, type RSIOptions } from '../indicators/RSI'
import { calculateMACD, type MACDOptions } from '../indicators/MACD'
import { calculateBollingerBands, type BollingerBandsOptions } from '../indicators/BollingerBands'
import './AdvancedTradingChart.css'

interface AdvancedTradingChartProps {
  data: ChartData[]
  symbol: string
  width?: number
  height?: number
}

interface IndicatorConfig {
  id: string
  name: string
  enabled: boolean
  type: 'MA' | 'RSI' | 'MACD' | 'BB'
  options: any
}

const AdvancedTradingChart: React.FC<AdvancedTradingChartProps> = ({ 
  data, 
  symbol, 
  width = 1000, 
  height = 600 
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null)
  const chartRef = useRef<IChartApi | null>(null)
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null)
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null)
  const indicatorSeriesRefs = useRef<Map<string, ISeriesApi<any>>>(new Map())
  
  const [indicators, setIndicators] = useState<IndicatorConfig[]>([
    {
      id: 'sma20',
      name: 'SMA 20',
      enabled: true,
      type: 'MA',
      options: { period: 20, type: 'SMA', color: '#2962ff', lineWidth: 2 } as MovingAverageOptions
    },
    {
      id: 'ema50',
      name: 'EMA 50',
      enabled: true,
      type: 'MA',
      options: { period: 50, type: 'EMA', color: '#ff6d00', lineWidth: 2 } as MovingAverageOptions
    },
    {
      id: 'bb',
      name: 'Bollinger Bands',
      enabled: false,
      type: 'BB',
      options: { 
        period: 20, 
        standardDeviations: 2, 
        upperColor: '#e91e63', 
        middleColor: '#9c27b0', 
        lowerColor: '#e91e63',
        fillColor: 'rgba(233, 30, 99, 0.1)'
      } as BollingerBandsOptions
    }
  ])

  const [showRSI, setShowRSI] = useState(false)
  const [showMACD, setShowMACD] = useState(false)

  useEffect(() => {
    if (!chartContainerRef.current) return

    // Create main chart
    const chart = createChart(chartContainerRef.current, {
      width,
      height: showRSI || showMACD ? height * 0.7 : height,
      layout: {
        background: { type: ColorType.Solid, color: '#1e1e1e' },
        textColor: '#d1d4dc',
      },
      grid: {
        vertLines: { color: '#2a2a2a' },
        horzLines: { color: '#2a2a2a' },
      },
      crosshair: {
        mode: CrosshairMode.Normal,
      },
      rightPriceScale: {
        borderColor: '#485c7b',
      },
      timeScale: {
        borderColor: '#485c7b',
        timeVisible: true,
        secondsVisible: false,
      },
    })

    chartRef.current = chart

    // Add candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#4caf50',
      downColor: '#f44336',
      borderDownColor: '#f44336',
      borderUpColor: '#4caf50',
      wickDownColor: '#f44336',
      wickUpColor: '#4caf50',
    })

    candlestickSeriesRef.current = candlestickSeries

    // Add volume series
    const volumeSeries = chart.addHistogramSeries({
      color: '#26a69a',
      priceFormat: {
        type: 'volume',
      },
      priceScaleId: '',
    })

    volumeSeriesRef.current = volumeSeries

    // Position volume series at the bottom
    chart.priceScale('').applyOptions({
      scaleMargins: {
        top: 0.8,
        bottom: 0,
      },
    })

    // Create RSI chart if enabled
    let rsiChart: IChartApi | null = null
    if (showRSI) {
      const rsiContainer = document.createElement('div')
      rsiContainer.style.height = `${height * 0.15}px`
      chartContainerRef.current.appendChild(rsiContainer)
      
      rsiChart = createChart(rsiContainer, {
        width,
        height: height * 0.15,
        layout: {
          background: { type: ColorType.Solid, color: '#1e1e1e' },
          textColor: '#d1d4dc',
        },
        grid: {
          vertLines: { color: '#2a2a2a' },
          horzLines: { color: '#2a2a2a' },
        },
        rightPriceScale: {
          borderColor: '#485c7b',
        },
        timeScale: {
          borderColor: '#485c7b',
          visible: false,
        },
      })
    }

    // Create MACD chart if enabled
    let macdChart: IChartApi | null = null
    if (showMACD) {
      const macdContainer = document.createElement('div')
      macdContainer.style.height = `${height * 0.15}px`
      chartContainerRef.current.appendChild(macdContainer)
      
      macdChart = createChart(macdContainer, {
        width,
        height: height * 0.15,
        layout: {
          background: { type: ColorType.Solid, color: '#1e1e1e' },
          textColor: '#d1d4dc',
        },
        grid: {
          vertLines: { color: '#2a2a2a' },
          horzLines: { color: '#2a2a2a' },
        },
        rightPriceScale: {
          borderColor: '#485c7b',
        },
        timeScale: {
          borderColor: '#485c7b',
          visible: false,
        },
      })
    }

    return () => {
      if (chart) chart.remove()
      if (rsiChart) rsiChart.remove()
      if (macdChart) macdChart.remove()
      indicatorSeriesRefs.current.clear()
    }
  }, [width, height, showRSI, showMACD])

  useEffect(() => {
    if (!candlestickSeriesRef.current || !volumeSeriesRef.current || !chartRef.current) return

    // Set main chart data
    const candlestickData: CandlestickData[] = data.map(item => ({
      time: item.time,
      open: item.open,
      high: item.high,
      low: item.low,
      close: item.close,
    }))

    const volumeData: HistogramData[] = data.map(item => ({
      time: item.time,
      value: item.volume,
      color: item.close >= item.open ? '#4caf5080' : '#f4433680',
    }))

    candlestickSeriesRef.current.setData(candlestickData)
    volumeSeriesRef.current.setData(volumeData)

    // Add indicators
    indicators.forEach(indicator => {
      if (!indicator.enabled) return

      const existingSeries = indicatorSeriesRefs.current.get(indicator.id)
      if (existingSeries) {
        chartRef.current?.removeSeries(existingSeries)
      }

      if (indicator.type === 'MA') {
        const maData = calculateMovingAverage(data, indicator.options)
        const lineData: LineData[] = maData.map(item => ({
          time: item.time,
          value: item.value,
        }))

        const maSeries = chartRef.current!.addLineSeries({
          color: indicator.options.color,
          lineWidth: indicator.options.lineWidth,
          title: indicator.name,
        })

        maSeries.setData(lineData)
        indicatorSeriesRefs.current.set(indicator.id, maSeries)
      }

      if (indicator.type === 'BB') {
        const bbData = calculateBollingerBands(data, indicator.options.period, indicator.options.standardDeviations)
        
        // Upper band
        const upperSeries = chartRef.current!.addLineSeries({
          color: indicator.options.upperColor,
          lineWidth: 1,
          title: 'BB Upper',
        })
        upperSeries.setData(bbData.map(item => ({ time: item.time, value: item.upper })))

        // Middle band
        const middleSeries = chartRef.current!.addLineSeries({
          color: indicator.options.middleColor,
          lineWidth: 1,
          title: 'BB Middle',
        })
        middleSeries.setData(bbData.map(item => ({ time: item.time, value: item.middle })))

        // Lower band
        const lowerSeries = chartRef.current!.addLineSeries({
          color: indicator.options.lowerColor,
          lineWidth: 1,
          title: 'BB Lower',
        })
        lowerSeries.setData(bbData.map(item => ({ time: item.time, value: item.lower })))

        indicatorSeriesRefs.current.set(`${indicator.id}_upper`, upperSeries)
        indicatorSeriesRefs.current.set(`${indicator.id}_middle`, middleSeries)
        indicatorSeriesRefs.current.set(`${indicator.id}_lower`, lowerSeries)
      }
    })

    // Fit content
    chartRef.current.timeScale().fitContent()
  }, [data, indicators])

  const toggleIndicator = (indicatorId: string) => {
    setIndicators(prev => prev.map(ind => 
      ind.id === indicatorId ? { ...ind, enabled: !ind.enabled } : ind
    ))
  }

  return (
    <div className="advanced-trading-chart">
      <div className="chart-controls">
        <div className="chart-info">
          <h3>{symbol}</h3>
          <span className="chart-type">Advanced Chart</span>
        </div>
        
        <div className="indicator-controls">
          {indicators.map(indicator => (
            <label key={indicator.id} className="indicator-toggle">
              <input
                type="checkbox"
                checked={indicator.enabled}
                onChange={() => toggleIndicator(indicator.id)}
              />
              <span style={{ color: indicator.options.color }}>{indicator.name}</span>
            </label>
          ))}
          
          <label className="indicator-toggle">
            <input
              type="checkbox"
              checked={showRSI}
              onChange={(e) => setShowRSI(e.target.checked)}
            />
            <span>RSI</span>
          </label>
          
          <label className="indicator-toggle">
            <input
              type="checkbox"
              checked={showMACD}
              onChange={(e) => setShowMACD(e.target.checked)}
            />
            <span>MACD</span>
          </label>
        </div>
      </div>
      
      <div ref={chartContainerRef} className="chart-container" />
    </div>
  )
}

export default AdvancedTradingChart
