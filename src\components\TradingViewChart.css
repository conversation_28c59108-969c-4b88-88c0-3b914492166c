.chart-wrapper {
  position: relative;
  background-color: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.chart-wrapper.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  border-radius: 0;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #404040;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.chart-type {
  background-color: #404040;
  color: #b3b3b3;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
}

.chart-buttons {
  display: flex;
  gap: 8px;
}

.control-btn {
  background-color: #404040;
  color: #ffffff;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.control-btn:hover {
  background-color: #505050;
}

.control-btn:active {
  background-color: #2962ff;
}

.chart-container {
  position: relative;
  background-color: #1e1e1e;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .chart-info {
    justify-content: center;
  }
  
  .chart-buttons {
    justify-content: center;
  }
  
  .chart-wrapper:not(.fullscreen) .chart-container {
    width: 100% !important;
    height: 400px !important;
  }
}
