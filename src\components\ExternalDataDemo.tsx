import React, { useState, useEffect } from 'react'
import { externalDataService, type SentimentData, type NewsEvent, type ExternalIndicatorData } from '../services/ExternalDataService'
import './ExternalDataDemo.css'

interface ExternalDataDemoProps {
  symbol: string
}

const ExternalDataDemo: React.FC<ExternalDataDemoProps> = ({ symbol }) => {
  const [sentimentData, setSentimentData] = useState<SentimentData[]>([])
  const [newsEvents, setNewsEvents] = useState<NewsEvent[]>([])
  const [customIndicators, setCustomIndicators] = useState<{
    fearGreed: ExternalIndicatorData[]
    putCall: ExternalIndicatorData[]
    optionsFlow: ExternalIndicatorData[]
  }>({
    fearGreed: [],
    putCall: [],
    optionsFlow: []
  })
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadExternalData()
  }, [symbol])

  const loadExternalData = async () => {
    setLoading(true)
    try {
      // Fetch all external data sources
      const [sentiment, news, fearGreed, putCall, optionsFlow] = await Promise.all([
        externalDataService.fetchSentimentIndicator(symbol),
        externalDataService.fetchNewsEvents(symbol, 7),
        externalDataService.fetchCustomIndicator(symbol, 'fear_greed_index', { period: 30 }),
        externalDataService.fetchCustomIndicator(symbol, 'put_call_ratio', { period: 20 }),
        externalDataService.fetchOptionsFlow(symbol)
      ])

      setSentimentData(sentiment)
      setNewsEvents(news)
      setCustomIndicators({
        fearGreed,
        putCall,
        optionsFlow
      })
    } catch (error) {
      console.error('Error loading external data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSentimentColor = (sentiment: number) => {
    if (sentiment > 0.3) return '#4caf50'
    if (sentiment < -0.3) return '#f44336'
    return '#ff9800'
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return '#f44336'
      case 'medium': return '#ff9800'
      case 'low': return '#4caf50'
      default: return '#b3b3b3'
    }
  }

  const getLatestValue = (data: ExternalIndicatorData[]) => {
    return data.length > 0 ? data[data.length - 1].value : 0
  }

  if (loading) {
    return (
      <div className="external-data-demo loading">
        <div className="loading-spinner">Loading external data...</div>
      </div>
    )
  }

  return (
    <div className="external-data-demo">
      <h3>External Data Integration for {symbol}</h3>
      
      <div className="data-grid">
        {/* Sentiment Analysis */}
        <div className="data-card">
          <h4>📊 Sentiment Analysis</h4>
          <div className="sentiment-overview">
            {sentimentData.slice(-5).map((item, index) => (
              <div key={index} className="sentiment-item">
                <span className="date">{new Date(item.time).toLocaleDateString()}</span>
                <div 
                  className="sentiment-bar"
                  style={{
                    backgroundColor: getSentimentColor(item.sentiment),
                    width: `${Math.abs(item.sentiment) * 50 + 10}px`
                  }}
                >
                  {(item.sentiment * 100).toFixed(1)}%
                </div>
                <span className="volume">{item.volume} mentions</span>
              </div>
            ))}
          </div>
        </div>

        {/* News Events */}
        <div className="data-card">
          <h4>📰 Recent News Events</h4>
          <div className="news-list">
            {newsEvents.slice(0, 4).map((event, index) => (
              <div key={index} className="news-item">
                <div className="news-header">
                  <span 
                    className="impact-badge"
                    style={{ backgroundColor: getImpactColor(event.impact) }}
                  >
                    {event.impact}
                  </span>
                  <span className="news-date">{new Date(event.time).toLocaleDateString()}</span>
                </div>
                <div className="news-title">{event.title}</div>
                <div className="news-meta">
                  <span className={`sentiment ${event.sentiment}`}>
                    {event.sentiment}
                  </span>
                  <span className="source">{event.source}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Custom Indicators */}
        <div className="data-card">
          <h4>🔍 Custom Indicators</h4>
          <div className="indicators-grid">
            <div className="indicator-item">
              <div className="indicator-label">Fear & Greed Index</div>
              <div className="indicator-value">
                {getLatestValue(customIndicators.fearGreed).toFixed(1)}
              </div>
              <div className="indicator-description">
                Market sentiment indicator (0-100)
              </div>
            </div>
            
            <div className="indicator-item">
              <div className="indicator-label">Put/Call Ratio</div>
              <div className="indicator-value">
                {getLatestValue(customIndicators.putCall).toFixed(2)}
              </div>
              <div className="indicator-description">
                Options market sentiment
              </div>
            </div>
            
            <div className="indicator-item">
              <div className="indicator-label">Options Flow</div>
              <div className="indicator-value">
                {getLatestValue(customIndicators.optionsFlow).toFixed(0)}
              </div>
              <div className="indicator-description">
                Unusual options activity
              </div>
            </div>
          </div>
        </div>

        {/* Data Sources */}
        <div className="data-card">
          <h4>🔗 Data Sources</h4>
          <div className="sources-list">
            <div className="source-item">
              <span className="source-name">Social Media Sentiment</span>
              <span className="source-status active">Active</span>
            </div>
            <div className="source-item">
              <span className="source-name">Financial News</span>
              <span className="source-status active">Active</span>
            </div>
            <div className="source-item">
              <span className="source-name">Options Market Data</span>
              <span className="source-status active">Active</span>
            </div>
            <div className="source-item">
              <span className="source-name">Economic Indicators</span>
              <span className="source-status inactive">Demo Mode</span>
            </div>
          </div>
        </div>
      </div>

      <div className="data-actions">
        <button onClick={loadExternalData} className="refresh-data-btn">
          🔄 Refresh External Data
        </button>
        <button className="export-data-btn">
          📊 Export to Chart
        </button>
      </div>
    </div>
  )
}

export default ExternalDataDemo
