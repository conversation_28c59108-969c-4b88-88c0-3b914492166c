.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;
}

.spinner {
  position: relative;
  display: inline-block;
}

.spinner.small {
  width: 32px;
  height: 32px;
}

.spinner.medium {
  width: 48px;
  height: 48px;
}

.spinner.large {
  width: 64px;
  height: 64px;
}

.spinner-ring {
  position: absolute;
  border: 3px solid transparent;
  border-top: 3px solid #2962ff;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
}

.spinner.small .spinner-ring {
  width: 32px;
  height: 32px;
  border-width: 2px;
  border-top-width: 2px;
}

.spinner.medium .spinner-ring {
  width: 48px;
  height: 48px;
  border-width: 3px;
  border-top-width: 3px;
}

.spinner.large .spinner-ring {
  width: 64px;
  height: 64px;
  border-width: 4px;
  border-top-width: 4px;
}

.spinner-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.3s;
  border-top-color: #4caf50;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.15s;
  border-top-color: #ff9800;
}

.spinner-ring:nth-child(4) {
  border-top-color: #f44336;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-message {
  margin: 0;
  color: #b3b3b3;
  font-size: 14px;
  text-align: center;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}
