import { useState } from 'react'
import TradingViewChart from './components/TradingViewChart'
import AdvancedTradingChart from './components/AdvancedTradingChart'
import ExternalDataDemo from './components/ExternalDataDemo'
import LoadingSpinner from './components/LoadingSpinner'
import type { ChartData } from './types/tradingview'
import { generateSampleData } from './data/sampleData'
import './App.css'

function App() {
  const [isLoading, setIsLoading] = useState(false)
  const [chartData, setChartData] = useState<ChartData[]>(generateSampleData())
  const [symbol, setSymbol] = useState('AAPL')
  const [chartType, setChartType] = useState<'basic' | 'advanced'>('advanced')
  const [showExternalData, setShowExternalData] = useState(false)

  const handleSymbolChange = (newSymbol: string) => {
    setIsLoading(true)
    setSymbol(newSymbol)

    // Simulate data loading
    setTimeout(() => {
      setChartData(generateSampleData())
      setIsLoading(false)
    }, 1000)
  }

  const handleRefreshData = () => {
    setIsLoading(true)
    setTimeout(() => {
      setChartData(generateSampleData())
      setIsLoading(false)
    }, 500)
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>TradingView Lightweight Charts</h1>
        <p>Professional financial charts with React & TypeScript</p>

        <div className="controls">
          <div className="symbol-selector">
            <label htmlFor="symbol">Symbol:</label>
            <select
              id="symbol"
              value={symbol}
              onChange={(e) => handleSymbolChange(e.target.value)}
              disabled={isLoading}
            >
              <option value="AAPL">AAPL</option>
              <option value="GOOGL">GOOGL</option>
              <option value="MSFT">MSFT</option>
              <option value="TSLA">TSLA</option>
              <option value="AMZN">AMZN</option>
            </select>
          </div>

          <div className="chart-type-selector">
            <label htmlFor="chartType">Chart Type:</label>
            <select
              id="chartType"
              value={chartType}
              onChange={(e) => setChartType(e.target.value as 'basic' | 'advanced')}
              disabled={isLoading}
            >
              <option value="basic">Basic Chart</option>
              <option value="advanced">Advanced Chart with Indicators</option>
            </select>
          </div>

          <button
            onClick={handleRefreshData}
            disabled={isLoading}
            className="refresh-btn"
          >
            {isLoading ? 'Loading...' : 'Refresh Data'}
          </button>

          <button
            onClick={() => setShowExternalData(!showExternalData)}
            className="external-data-btn"
          >
            {showExternalData ? 'Hide' : 'Show'} External Data
          </button>
        </div>
      </header>

      <main className="chart-container">
        {isLoading ? (
          <LoadingSpinner />
        ) : chartType === 'advanced' ? (
          <AdvancedTradingChart
            data={chartData}
            symbol={symbol}
            width={1200}
            height={700}
          />
        ) : (
          <TradingViewChart
            data={chartData}
            symbol={symbol}
          />
        )}
      </main>

      {showExternalData && (
        <ExternalDataDemo symbol={symbol} />
      )}

      <footer className="app-footer">
        <p>
          Built with{' '}
          <a
            href="https://github.com/tradingview/lightweight-charts"
            target="_blank"
            rel="noopener noreferrer"
          >
            TradingView Lightweight Charts
          </a>
          {' '}• React • TypeScript • Vite
        </p>
      </footer>
    </div>
  )
}

export default App
