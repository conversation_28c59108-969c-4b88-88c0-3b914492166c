import React, { useState } from 'react'
import TradingViewChart from './components/TradingViewChart'
import LoadingSpinner from './components/LoadingSpinner'
import { ChartData } from './types/tradingview'
import { generateSampleData } from './data/sampleData'
import './App.css'

function App() {
  const [isLoading, setIsLoading] = useState(false)
  const [chartData, setChartData] = useState<ChartData[]>(generateSampleData())
  const [symbol, setSymbol] = useState('AAPL')

  const handleSymbolChange = (newSymbol: string) => {
    setIsLoading(true)
    setSymbol(newSymbol)

    // Simulate data loading
    setTimeout(() => {
      setChartData(generateSampleData())
      setIsLoading(false)
    }, 1000)
  }

  const handleRefreshData = () => {
    setIsLoading(true)
    setTimeout(() => {
      setChartData(generateSampleData())
      setIsLoading(false)
    }, 500)
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>TradingView Lightweight Charts</h1>
        <p>Professional financial charts with React & TypeScript</p>

        <div className="controls">
          <div className="symbol-selector">
            <label htmlFor="symbol">Symbol:</label>
            <select
              id="symbol"
              value={symbol}
              onChange={(e) => handleSymbolChange(e.target.value)}
              disabled={isLoading}
            >
              <option value="AAPL">AAPL</option>
              <option value="GOOGL">GOOGL</option>
              <option value="MSFT">MSFT</option>
              <option value="TSLA">TSLA</option>
              <option value="AMZN">AMZN</option>
            </select>
          </div>

          <button
            onClick={handleRefreshData}
            disabled={isLoading}
            className="refresh-btn"
          >
            {isLoading ? 'Loading...' : 'Refresh Data'}
          </button>
        </div>
      </header>

      <main className="chart-container">
        {isLoading ? (
          <LoadingSpinner />
        ) : (
          <TradingViewChart
            data={chartData}
            symbol={symbol}
          />
        )}
      </main>

      <footer className="app-footer">
        <p>
          Built with{' '}
          <a
            href="https://github.com/tradingview/lightweight-charts"
            target="_blank"
            rel="noopener noreferrer"
          >
            TradingView Lightweight Charts
          </a>
          {' '}• React • TypeScript • Vite
        </p>
      </footer>
    </div>
  )
}

export default App
