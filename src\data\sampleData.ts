import type { ChartData } from '../types/tradingview'

// Generate realistic sample financial data
export function generateSampleData(days: number = 100): ChartData[] {
  const data: ChartData[] = []
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - days)
  
  let currentPrice = 150 + Math.random() * 100 // Start between $150-$250
  
  for (let i = 0; i < days; i++) {
    const date = new Date(startDate)
    date.setDate(date.getDate() + i)
    
    // Skip weekends for more realistic stock data
    if (date.getDay() === 0 || date.getDay() === 6) {
      continue
    }
    
    // Generate daily price movement (more realistic volatility)
    const volatility = 0.02 // 2% daily volatility
    const trend = (Math.random() - 0.5) * 0.001 // Small random trend
    const dailyChange = (Math.random() - 0.5) * volatility + trend
    
    // Calculate OHLC values
    const open = currentPrice
    const changeRange = Math.abs(dailyChange * currentPrice)
    const high = open + Math.random() * changeRange * 1.5
    const low = open - Math.random() * changeRange * 1.5
    const close = open + (dailyChange * currentPrice)
    
    // Ensure OHLC relationships are valid
    const validHigh = Math.max(open, close, high)
    const validLow = Math.min(open, close, low)
    
    // Generate volume (higher volume on bigger price moves)
    const baseVolume = 1000000 + Math.random() * 2000000
    const volumeMultiplier = 1 + Math.abs(dailyChange) * 5
    const volume = Math.floor(baseVolume * volumeMultiplier)
    
    data.push({
      time: date.toISOString().split('T')[0], // YYYY-MM-DD format
      open: Math.round(open * 100) / 100,
      high: Math.round(validHigh * 100) / 100,
      low: Math.round(validLow * 100) / 100,
      close: Math.round(close * 100) / 100,
      volume: volume
    })
    
    currentPrice = close
  }
  
  return data
}

// Generate intraday data (for future use)
export function generateIntradayData(hours: number = 24): ChartData[] {
  const data: ChartData[] = []
  const startTime = new Date()
  startTime.setHours(startTime.getHours() - hours)
  
  let currentPrice = 150 + Math.random() * 100
  
  for (let i = 0; i < hours * 4; i++) { // 15-minute intervals
    const time = new Date(startTime)
    time.setMinutes(time.getMinutes() + (i * 15))
    
    // Generate 15-minute price movement
    const volatility = 0.005 // 0.5% per 15-minute period
    const dailyChange = (Math.random() - 0.5) * volatility
    
    const open = currentPrice
    const changeRange = Math.abs(dailyChange * currentPrice)
    const high = open + Math.random() * changeRange * 1.2
    const low = open - Math.random() * changeRange * 1.2
    const close = open + (dailyChange * currentPrice)
    
    const validHigh = Math.max(open, close, high)
    const validLow = Math.min(open, close, low)
    
    const baseVolume = 50000 + Math.random() * 100000
    const volumeMultiplier = 1 + Math.abs(dailyChange) * 3
    const volume = Math.floor(baseVolume * volumeMultiplier)
    
    data.push({
      time: time.toISOString(),
      open: Math.round(open * 100) / 100,
      high: Math.round(validHigh * 100) / 100,
      low: Math.round(validLow * 100) / 100,
      close: Math.round(close * 100) / 100,
      volume: volume
    })
    
    currentPrice = close
  }
  
  return data
}

// Simulate real-time price updates
export function generatePriceUpdate(lastPrice: number): number {
  const volatility = 0.001 // 0.1% volatility per update
  const change = (Math.random() - 0.5) * volatility
  return Math.round((lastPrice * (1 + change)) * 100) / 100
}

// Get sample symbols with different characteristics
export function getSampleSymbols() {
  return [
    {
      symbol: 'AAPL',
      name: 'Apple Inc.',
      basePrice: 180,
      volatility: 0.02
    },
    {
      symbol: 'GOOGL',
      name: 'Alphabet Inc.',
      basePrice: 140,
      volatility: 0.025
    },
    {
      symbol: 'MSFT',
      name: 'Microsoft Corporation',
      basePrice: 380,
      volatility: 0.018
    },
    {
      symbol: 'TSLA',
      name: 'Tesla, Inc.',
      basePrice: 250,
      volatility: 0.04
    },
    {
      symbol: 'AMZN',
      name: 'Amazon.com, Inc.',
      basePrice: 160,
      volatility: 0.022
    }
  ]
}
