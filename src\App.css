.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background-color: var(--background-light);
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  color: var(--text-primary);
}

.app-header p {
  margin: 0 0 20px 0;
  color: var(--text-secondary);
  font-size: 16px;
}

.controls {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.symbol-selector,
.chart-type-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.symbol-selector label,
.chart-type-selector label {
  font-weight: 500;
  color: var(--text-primary);
}

.symbol-selector select {
  min-width: 120px;
}

.chart-type-selector select {
  min-width: 200px;
}

.refresh-btn,
.external-data-btn {
  background-color: var(--primary-color);
  color: white;
  font-weight: 500;
  padding: 10px 20px;
}

.refresh-btn:hover:not(:disabled),
.external-data-btn:hover:not(:disabled) {
  background-color: #1e4fd6;
}

.external-data-btn {
  background-color: #4caf50;
}

.external-data-btn:hover:not(:disabled) {
  background-color: #45a049;
}

.chart-container {
  flex: 1;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
}

.app-footer {
  background-color: var(--background-light);
  padding: 16px 20px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.app-footer p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 16px;
  }
  
  .app-header h1 {
    font-size: 24px;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .symbol-selector {
    justify-content: space-between;
  }
  
  .chart-container {
    padding: 16px;
    min-height: 400px;
  }
}
