.advanced-trading-chart {
  background-color: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #404040;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-info h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.chart-type {
  background-color: #404040;
  color: #b3b3b3;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 500;
}

.indicator-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.indicator-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #d1d4dc;
  user-select: none;
}

.indicator-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #2962ff;
  cursor: pointer;
}

.indicator-toggle span {
  font-weight: 500;
}

.indicator-toggle:hover span {
  color: #ffffff;
}

.chart-container {
  position: relative;
  background-color: #1e1e1e;
}

/* Indicator-specific styles */
.indicator-panel {
  border-top: 1px solid #404040;
  background-color: #1e1e1e;
}

.indicator-panel-header {
  padding: 8px 16px;
  background-color: #2a2a2a;
  border-bottom: 1px solid #404040;
  font-size: 12px;
  font-weight: 600;
  color: #b3b3b3;
  text-transform: uppercase;
}

/* Custom scrollbar for indicator controls */
.indicator-controls::-webkit-scrollbar {
  height: 4px;
}

.indicator-controls::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.indicator-controls::-webkit-scrollbar-thumb {
  background: #404040;
  border-radius: 2px;
}

.indicator-controls::-webkit-scrollbar-thumb:hover {
  background: #505050;
}

/* Responsive design */
@media (max-width: 768px) {
  .chart-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .chart-info {
    justify-content: center;
  }
  
  .indicator-controls {
    justify-content: center;
    gap: 12px;
  }
  
  .indicator-toggle {
    font-size: 13px;
  }
}

/* Animation for indicator toggles */
.indicator-toggle {
  transition: all 0.2s ease;
}

.indicator-toggle:hover {
  transform: translateY(-1px);
}

/* Custom checkbox styling */
.indicator-toggle input[type="checkbox"] {
  appearance: none;
  background-color: #404040;
  border: 2px solid #404040;
  border-radius: 3px;
  width: 16px;
  height: 16px;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

.indicator-toggle input[type="checkbox"]:checked {
  background-color: #2962ff;
  border-color: #2962ff;
}

.indicator-toggle input[type="checkbox"]:checked::after {
  content: '✓';
  position: absolute;
  top: -2px;
  left: 2px;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.indicator-toggle input[type="checkbox"]:hover {
  border-color: #2962ff;
}
