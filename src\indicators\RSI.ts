import type { ChartData } from '../types/tradingview'

export interface RSIData {
  time: string
  value: number
}

export interface RSIOptions {
  period: number
  overbought: number
  oversold: number
  color: string
  overboughtColor: string
  oversoldColor: string
}

/**
 * Relative Strength Index (RSI) calculation
 */
export function calculateRSI(data: ChartData[], period: number = 14): RSIData[] {
  if (data.length < period + 1) return []
  
  const result: RSIData[] = []
  const gains: number[] = []
  const losses: number[] = []
  
  // Calculate initial gains and losses
  for (let i = 1; i < data.length; i++) {
    const change = data[i].close - data[i - 1].close
    gains.push(change > 0 ? change : 0)
    losses.push(change < 0 ? Math.abs(change) : 0)
  }
  
  // Calculate initial average gain and loss
  let avgGain = gains.slice(0, period).reduce((sum, gain) => sum + gain, 0) / period
  let avgLoss = losses.slice(0, period).reduce((sum, loss) => sum + loss, 0) / period
  
  // Calculate first RSI value
  let rs = avgGain / (avgLoss || 1)
  let rsi = 100 - (100 / (1 + rs))
  
  result.push({
    time: data[period].time,
    value: rsi
  })
  
  // Calculate subsequent RSI values using smoothed averages
  for (let i = period + 1; i < data.length; i++) {
    const change = data[i].close - data[i - 1].close
    const gain = change > 0 ? change : 0
    const loss = change < 0 ? Math.abs(change) : 0
    
    // Smoothed averages
    avgGain = (avgGain * (period - 1) + gain) / period
    avgLoss = (avgLoss * (period - 1) + loss) / period
    
    rs = avgGain / (avgLoss || 1)
    rsi = 100 - (100 / (1 + rs))
    
    result.push({
      time: data[i].time,
      value: rsi
    })
  }
  
  return result
}

/**
 * Get RSI signal based on overbought/oversold levels
 */
export function getRSISignal(rsi: number, options: RSIOptions): 'overbought' | 'oversold' | 'neutral' {
  if (rsi >= options.overbought) return 'overbought'
  if (rsi <= options.oversold) return 'oversold'
  return 'neutral'
}

/**
 * Calculate RSI divergence signals
 */
export function calculateRSIDivergence(
  priceData: ChartData[], 
  rsiData: RSIData[], 
  lookback: number = 5
): Array<{ time: string; type: 'bullish' | 'bearish' }> {
  const signals: Array<{ time: string; type: 'bullish' | 'bearish' }> = []
  
  if (priceData.length < lookback * 2 || rsiData.length < lookback * 2) return signals
  
  for (let i = lookback; i < Math.min(priceData.length, rsiData.length) - lookback; i++) {
    const currentPrice = priceData[i].close
    const currentRSI = rsiData[i - lookback]?.value || 0
    
    // Look for previous peaks/troughs
    for (let j = i - lookback; j >= lookback; j--) {
      const prevPrice = priceData[j].close
      const prevRSI = rsiData[j - lookback]?.value || 0
      
      // Bullish divergence: price makes lower low, RSI makes higher low
      if (currentPrice < prevPrice && currentRSI > prevRSI && currentRSI < 40) {
        signals.push({
          time: priceData[i].time,
          type: 'bullish'
        })
        break
      }
      
      // Bearish divergence: price makes higher high, RSI makes lower high
      if (currentPrice > prevPrice && currentRSI < prevRSI && currentRSI > 60) {
        signals.push({
          time: priceData[i].time,
          type: 'bearish'
        })
        break
      }
    }
  }
  
  return signals
}
