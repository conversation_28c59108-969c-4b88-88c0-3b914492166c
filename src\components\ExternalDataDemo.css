.external-data-demo {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  color: #d1d4dc;
}

.external-data-demo.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loading-spinner {
  color: #2962ff;
  font-size: 16px;
}

.external-data-demo h3 {
  margin: 0 0 20px 0;
  color: #ffffff;
  font-size: 20px;
  border-bottom: 2px solid #2962ff;
  padding-bottom: 10px;
}

.data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.data-card {
  background-color: #2a2a2a;
  border-radius: 6px;
  padding: 16px;
  border: 1px solid #404040;
}

.data-card h4 {
  margin: 0 0 16px 0;
  color: #ffffff;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Sentiment Analysis */
.sentiment-overview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sentiment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background-color: #1e1e1e;
  border-radius: 4px;
}

.sentiment-item .date {
  font-size: 12px;
  color: #b3b3b3;
  min-width: 80px;
}

.sentiment-bar {
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 11px;
  font-weight: bold;
  min-width: 50px;
}

.sentiment-item .volume {
  font-size: 11px;
  color: #b3b3b3;
}

/* News Events */
.news-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.news-item {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 12px;
  border-left: 3px solid #2962ff;
}

.news-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.impact-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  color: white;
}

.news-date {
  font-size: 11px;
  color: #b3b3b3;
}

.news-title {
  font-size: 14px;
  color: #ffffff;
  margin-bottom: 8px;
  line-height: 1.4;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
}

.sentiment.positive {
  color: #4caf50;
}

.sentiment.negative {
  color: #f44336;
}

.sentiment.neutral {
  color: #ff9800;
}

.source {
  color: #b3b3b3;
}

/* Custom Indicators */
.indicators-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.indicator-item {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 12px;
  text-align: center;
}

.indicator-label {
  font-size: 12px;
  color: #b3b3b3;
  margin-bottom: 8px;
}

.indicator-value {
  font-size: 24px;
  font-weight: bold;
  color: #2962ff;
  margin-bottom: 4px;
}

.indicator-description {
  font-size: 10px;
  color: #b3b3b3;
  line-height: 1.3;
}

/* Data Sources */
.sources-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #1e1e1e;
  border-radius: 4px;
}

.source-name {
  font-size: 14px;
  color: #ffffff;
}

.source-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.source-status.active {
  background-color: #4caf50;
  color: white;
}

.source-status.inactive {
  background-color: #ff9800;
  color: white;
}

/* Data Actions */
.data-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.refresh-data-btn,
.export-data-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-data-btn {
  background-color: #2962ff;
  color: white;
}

.refresh-data-btn:hover {
  background-color: #1e4fd6;
}

.export-data-btn {
  background-color: #404040;
  color: white;
}

.export-data-btn:hover {
  background-color: #505050;
}

/* Responsive design */
@media (max-width: 768px) {
  .data-grid {
    grid-template-columns: 1fr;
  }
  
  .indicators-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .data-actions {
    flex-direction: column;
  }
  
  .sentiment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }
  
  .news-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
