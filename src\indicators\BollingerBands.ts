import type { ChartData } from '../types/tradingview'
import { calculateSMA } from './MovingAverage'

export interface BollingerBandsData {
  time: string
  upper: number
  middle: number
  lower: number
  bandwidth: number
  percentB: number
}

export interface BollingerBandsOptions {
  period: number
  standardDeviations: number
  upperColor: string
  middleColor: string
  lowerColor: string
  fillColor: string
}

/**
 * Bollinger Bands calculation
 */
export function calculateBollingerBands(
  data: ChartData[], 
  period: number = 20, 
  standardDeviations: number = 2
): BollingerBandsData[] {
  if (data.length < period) return []
  
  const result: BollingerBandsData[] = []
  const smaData = calculateSMA(data, period)
  
  for (let i = 0; i < smaData.length; i++) {
    const dataIndex = i + period - 1
    const sma = smaData[i].value
    
    // Calculate standard deviation
    let sumSquaredDiffs = 0
    for (let j = 0; j < period; j++) {
      const diff = data[dataIndex - j].close - sma
      sumSquaredDiffs += diff * diff
    }
    const standardDeviation = Math.sqrt(sumSquaredDiffs / period)
    
    const upper = sma + (standardDeviations * standardDeviation)
    const lower = sma - (standardDeviations * standardDeviation)
    const currentPrice = data[dataIndex].close
    
    // Calculate %B (position within bands)
    const percentB = (currentPrice - lower) / (upper - lower)
    
    // Calculate bandwidth (width of bands relative to middle)
    const bandwidth = (upper - lower) / sma
    
    result.push({
      time: data[dataIndex].time,
      upper,
      middle: sma,
      lower,
      bandwidth,
      percentB
    })
  }
  
  return result
}

/**
 * Get Bollinger Bands signals
 */
export function getBollingerBandsSignals(
  priceData: ChartData[], 
  bandsData: BollingerBandsData[]
): Array<{
  time: string
  type: 'squeeze' | 'expansion' | 'upper_touch' | 'lower_touch' | 'middle_cross_up' | 'middle_cross_down'
  value?: number
}> {
  const signals: Array<{
    time: string
    type: 'squeeze' | 'expansion' | 'upper_touch' | 'lower_touch' | 'middle_cross_up' | 'middle_cross_down'
    value?: number
  }> = []
  
  if (bandsData.length < 2) return signals
  
  for (let i = 1; i < bandsData.length; i++) {
    const current = bandsData[i]
    const previous = bandsData[i - 1]
    const currentPrice = priceData[i + 19]?.close || 0 // Adjust for period offset
    const previousPrice = priceData[i + 18]?.close || 0
    
    // Bollinger Band squeeze (low volatility)
    if (current.bandwidth < 0.1 && previous.bandwidth >= 0.1) {
      signals.push({
        time: current.time,
        type: 'squeeze',
        value: current.bandwidth
      })
    }
    
    // Bollinger Band expansion (high volatility)
    if (current.bandwidth > 0.2 && previous.bandwidth <= 0.2) {
      signals.push({
        time: current.time,
        type: 'expansion',
        value: current.bandwidth
      })
    }
    
    // Price touching upper band
    if (currentPrice >= current.upper * 0.999) {
      signals.push({
        time: current.time,
        type: 'upper_touch',
        value: current.percentB
      })
    }
    
    // Price touching lower band
    if (currentPrice <= current.lower * 1.001) {
      signals.push({
        time: current.time,
        type: 'lower_touch',
        value: current.percentB
      })
    }
    
    // Price crossing middle band upward
    if (previousPrice <= previous.middle && currentPrice > current.middle) {
      signals.push({
        time: current.time,
        type: 'middle_cross_up'
      })
    }
    
    // Price crossing middle band downward
    if (previousPrice >= previous.middle && currentPrice < current.middle) {
      signals.push({
        time: current.time,
        type: 'middle_cross_down'
      })
    }
  }
  
  return signals
}

/**
 * Calculate Bollinger Band %B oscillator
 */
export function calculatePercentB(bandsData: BollingerBandsData[]): Array<{ time: string; value: number }> {
  return bandsData.map(band => ({
    time: band.time,
    value: band.percentB * 100 // Convert to percentage
  }))
}

/**
 * Calculate Bollinger Band Width oscillator
 */
export function calculateBandwidth(bandsData: BollingerBandsData[]): Array<{ time: string; value: number }> {
  return bandsData.map(band => ({
    time: band.time,
    value: band.bandwidth * 100 // Convert to percentage
  }))
}
