// Chart data interface for our application
export interface ChartData {
  time: string
  open: number
  high: number
  low: number
  close: number
  volume: number
}

// Chart configuration options
export interface ChartOptions {
  width?: number
  height?: number
  symbol?: string
  interval?: string
  theme?: 'light' | 'dark'
}

// Price data for real-time updates
export interface PriceUpdate {
  symbol: string
  price: number
  change: number
  changePercent: number
  timestamp: number
}

// Chart series types
export type SeriesType = 'candlestick' | 'line' | 'area' | 'histogram'

// Chart event types
export interface ChartEvent {
  type: 'click' | 'crosshairMove' | 'timeScaleVisibleRangeChange'
  data: any
}

// Datafeed interface (for future real data integration)
export interface DatafeedConfiguration {
  supported_resolutions: string[]
  supports_group_request: boolean
  supports_marks: boolean
  supports_search: boolean
  supports_timescale_marks: boolean
}

export interface SymbolInfo {
  name: string
  full_name: string
  description: string
  type: string
  session: string
  exchange: string
  listed_exchange: string
  timezone: string
  format: string
  pricescale: number
  minmov: number
  fractional: boolean
  minmove2: number
  currency_code: string
  original_currency_code: string
  unit_id: string
  original_unit_id: string
  unit_conversion_types: string[]
  original_type: string
  original_session: string
  original_exchange: string
  original_listed_exchange: string
  original_format: string
  original_pricescale: number
  original_minmov: number
  original_fractional: boolean
  original_minmove2: number
  has_intraday: boolean
  has_no_volume: boolean
  has_weekly_and_monthly: boolean
  has_empty_bars: boolean
  force_session_rebuild: boolean
  has_seconds: boolean
  seconds_multipliers: number[]
  has_daily: boolean
  intraday_multipliers: string[]
  weekly_multipliers: string[]
  monthly_multipliers: string[]
  supported_resolutions: string[]
  volume_precision: number
  data_status: string
  expired: boolean
  expiration_date: number
  sector: string
  industry: string
  currency_id: string
  original_currency_id: string
}
