import type { ChartData } from '../types/tradingview'
import { calculateEMA } from './MovingAverage'

export interface MACDData {
  time: string
  macd: number
  signal: number
  histogram: number
}

export interface MACDOptions {
  fastPeriod: number
  slowPeriod: number
  signalPeriod: number
  macdColor: string
  signalColor: string
  histogramUpColor: string
  histogramDownColor: string
}

/**
 * MACD (Moving Average Convergence Divergence) calculation
 */
export function calculateMACD(
  data: ChartData[], 
  fastPeriod: number = 12, 
  slowPeriod: number = 26, 
  signalPeriod: number = 9
): MACDData[] {
  if (data.length < slowPeriod) return []
  
  // Calculate fast and slow EMAs
  const fastEMA = calculateEMA(data, fastPeriod)
  const slowEMA = calculateEMA(data, slowPeriod)
  
  // Calculate MACD line (fast EMA - slow EMA)
  const macdLine: Array<{ time: string; value: number }> = []
  const startIndex = slowPeriod - fastPeriod
  
  for (let i = startIndex; i < fastEMA.length; i++) {
    const slowIndex = i - startIndex
    if (slowIndex < slowEMA.length) {
      macdLine.push({
        time: fastEMA[i].time,
        value: fastEMA[i].value - slowEMA[slowIndex].value
      })
    }
  }
  
  // Calculate signal line (EMA of MACD line)
  const signalLine = calculateEMAFromValues(macdLine, signalPeriod)
  
  // Calculate histogram (MACD - Signal)
  const result: MACDData[] = []
  const signalStartIndex = signalPeriod - 1
  
  for (let i = signalStartIndex; i < macdLine.length; i++) {
    const signalIndex = i - signalStartIndex
    if (signalIndex < signalLine.length) {
      const macdValue = macdLine[i].value
      const signalValue = signalLine[signalIndex].value
      
      result.push({
        time: macdLine[i].time,
        macd: macdValue,
        signal: signalValue,
        histogram: macdValue - signalValue
      })
    }
  }
  
  return result
}

/**
 * Calculate EMA from pre-calculated values
 */
function calculateEMAFromValues(
  data: Array<{ time: string; value: number }>, 
  period: number
): Array<{ time: string; value: number }> {
  const result: Array<{ time: string; value: number }> = []
  const multiplier = 2 / (period + 1)
  
  if (data.length === 0) return result
  
  // First EMA value is the SMA
  let ema = data[0].value
  for (let i = 1; i < Math.min(period, data.length); i++) {
    ema = (ema * (period - 1) + data[i].value) / period
  }
  
  result.push({
    time: data[Math.min(period - 1, data.length - 1)].time,
    value: ema
  })
  
  // Calculate EMA for remaining data points
  for (let i = period; i < data.length; i++) {
    ema = (data[i].value - ema) * multiplier + ema
    result.push({
      time: data[i].time,
      value: ema
    })
  }
  
  return result
}

/**
 * Get MACD signals
 */
export function getMACDSignals(macdData: MACDData[]): Array<{
  time: string
  type: 'bullish_crossover' | 'bearish_crossover' | 'bullish_divergence' | 'bearish_divergence'
}> {
  const signals: Array<{
    time: string
    type: 'bullish_crossover' | 'bearish_crossover' | 'bullish_divergence' | 'bearish_divergence'
  }> = []
  
  for (let i = 1; i < macdData.length; i++) {
    const current = macdData[i]
    const previous = macdData[i - 1]
    
    // Bullish crossover: MACD crosses above signal line
    if (previous.macd <= previous.signal && current.macd > current.signal) {
      signals.push({
        time: current.time,
        type: 'bullish_crossover'
      })
    }
    
    // Bearish crossover: MACD crosses below signal line
    if (previous.macd >= previous.signal && current.macd < current.signal) {
      signals.push({
        time: current.time,
        type: 'bearish_crossover'
      })
    }
  }
  
  return signals
}
